import 'package:flutter/material.dart';

import 'screens/main_screen.dart';
import 'service_locator/injection_container.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  initializeDependencies();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const MainScreen(),
    );
  }
}
